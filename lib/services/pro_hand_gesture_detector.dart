// lib/services/pro_hand_gesture_detector.dart
import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart' show CameraController; // for the nullable stub




enum GestureType { rock, paper, scissors, none, unknown }




class ProHandGestureDetector with ChangeNotifier {
static const String MODE_TRAINING = "training";
static const String MODE_GAME = "game";




static const MethodChannel _methods = MethodChannel('mp_gesture/methods');
static const EventChannel _events = EventChannel('mp_gesture/events');




final _gestureCtrl = StreamController<GestureType>.broadcast();
Stream<GestureType>? get gestureStream => _gestureCtrl.stream;




GestureType _currentGesture = GestureType.none;
double _currentConfidence = 0.0;
String _currentMode = MODE_GAME;
String _detectedHand = "Unknown"; // "Left", "Right", ou "Unknown"




// Align with app expectations
GestureType get currentGesture => _currentGesture;       // enum
String get currentGestureString => _currentGesture.name; // helper for UI
double get currentConfidence => _currentConfidence;
String get currentMode => _currentMode;
String get detectedHand => _detectedHand;                // main détectée automatiquement




// Some code references a cameraController; expose a nullable stub
CameraController? get cameraController => null;




bool _initialized = false;
bool get isInitialized => _initialized;




Future<bool> initialize() async {
  if (_initialized) return true;

  debugPrint('🎮 CAMERA: Initializing ProHandGestureDetector...');
  debugPrint('🎮 DEVICE: Running on ${Platform.operatingSystem} ${Platform.operatingSystemVersion}');

  try {
    // Test si on est sur émulateur
    bool isEmulator = await _isRunningOnEmulator();
    if (isEmulator) {
      debugPrint('⚠️ EMULATOR: Running on emulator, using fallback mode');
      _initialized = true;
      return true;
    }

    _events.receiveBroadcastStream().listen((dynamic e) {
      if (e is Map) {
        final label = (e['label'] as String?) ?? 'none';
        final score = (e['score'] as num?)?.toDouble() ?? 0.0;
        final hand = (e['hand'] as String?) ?? 'Unknown';

        _currentGesture = _mapLabel(label);
        _currentConfidence = score;
        _detectedHand = hand;

        // Log pour debug
        debugPrint('🎯 Geste: $label, Score: $score, Main: $hand');

        _gestureCtrl.add(_currentGesture);
        notifyListeners();
      }
    }, onError: (err) {
      debugPrint('🔴 mp_gesture/events error: $err');
    });

    _initialized = true;
    debugPrint('✅ CAMERA: ProHandGestureDetector initialized successfully');
    return true;
  } catch (e) {
    debugPrint('🔴 CAMERA: Failed to initialize ProHandGestureDetector: $e');
    // En cas d'erreur, on considère comme initialisé pour éviter le crash
    _initialized = true;
    return true;
  }
}

Future<bool> _isRunningOnEmulator() async {
  // SOLUTION TEMPORAIRE : Désactiver complètement la détection d'émulateur
  // pour forcer l'utilisation de la vraie détection sur tous les appareils
  debugPrint('🔍 EMULATOR CHECK: Forcing real device mode for all devices');
  return false;
}




Future<void> startDetection({String mode = MODE_GAME, double minScore = 0.65, bool useFrontCamera = true}) async {
  _currentMode = mode;
  debugPrint('🎮 CAMERA: Starting detection with mode: $mode, minScore: $minScore, useFrontCamera: $useFrontCamera');
  debugPrint('🎮 DEVICE: Running on ${Platform.operatingSystem} ${Platform.operatingSystemVersion}');

  // Vérifier si on est sur émulateur
  bool isEmulator = await _isRunningOnEmulator();
  if (isEmulator) {
    // Simulation seulement en mode debug
    if (kDebugMode) {
      debugPrint('⚠️ EMULATOR: Running on emulator, starting simulation mode (DEBUG ONLY)');
      _startEmulatorSimulation();
      return;
    } else {
      debugPrint('🚫 EMULATOR: Simulation disabled in release mode');
      // En mode release sur émulateur, on essaie quand même la vraie caméra
      // Si ça échoue, le système de récupération prendra le relais
    }
  }

  try {
    await _methods.invokeMethod('start', <String, dynamic>{
      'minScore': minScore,
      'useFrontCamera': useFrontCamera
    });
    debugPrint('✅ CAMERA: Detection started successfully');
  } catch (e) {
    debugPrint('🔴 CAMERA: startDetection error: $e');
    debugPrint('🔴 CAMERA: Error type: ${e.runtimeType}');
    if (e is PlatformException) {
      debugPrint('🔴 CAMERA: Platform error code: ${e.code}, message: ${e.message}');
    }

    // En cas d'erreur, démarrer le mode simulation SEULEMENT en debug
    if (kDebugMode) {
      debugPrint('🔄 CAMERA: Starting fallback simulation mode (DEBUG ONLY)');
      _startEmulatorSimulation();
    } else {
      debugPrint('🚫 CAMERA: Simulation disabled in release mode');
      // En mode release, on relance une erreur pour déclencher le système de récupération
      throw PlatformException(
        code: 'CAMERA_ERROR',
        message: 'Camera initialization failed in release mode',
      );
    }
  }
}




Future<void> stopDetection() async {
  // Arrêter la simulation si elle est active
  _stopEmulatorSimulation();

  try {
    await _methods.invokeMethod('stop');
  } catch (e) {
    debugPrint('stopDetection error: $e');
  }
}




String getGestureString() => _currentGesture.name; // backward-compat helper
GestureType getGesture() => _currentGesture;       // if some code expects enum




@override
void dispose() {
  _gestureCtrl.close(); // ChangeNotifier.dispose is sync
  super.dispose();
}




GestureType _mapLabel(String label) {
  final l = label.toLowerCase();
  if (l.contains('rock') || l.contains('closed_fist')) return GestureType.rock;
  if (l.contains('paper') || l.contains('open_palm')) return GestureType.paper;
  if (l.contains('scissors') || l.contains('victory')) return GestureType.scissors;
  if (l.contains('none') || l.isEmpty) return GestureType.none;
  return GestureType.unknown;
}

Timer? _simulationTimer;

void _startEmulatorSimulation() {
  debugPrint('🎭 SIMULATION: Starting emulator gesture simulation');

  _simulationTimer?.cancel();
  _simulationTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
    // Simuler des gestes aléatoirement pour l'émulateur
    final gestures = [GestureType.rock, GestureType.paper, GestureType.scissors];
    final randomGesture = gestures[DateTime.now().millisecond % gestures.length];
    final randomConfidence = 0.7 + (DateTime.now().millisecond % 30) / 100.0;

    _currentGesture = randomGesture;
    _currentConfidence = randomConfidence;
    _detectedHand = "Simulated";

    debugPrint('🎭 SIMULATION: Generated gesture: ${randomGesture.name}, confidence: $randomConfidence');

    _gestureCtrl.add(randomGesture);
    notifyListeners();
  });
}

void _stopEmulatorSimulation() {
  _simulationTimer?.cancel();
  _simulationTimer = null;
  debugPrint('🎭 SIMULATION: Stopped emulator simulation');
}
}















